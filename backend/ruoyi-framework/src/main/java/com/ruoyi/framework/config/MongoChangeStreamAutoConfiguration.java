package com.ruoyi.framework.config;

import com.ruoyi.system.config.ChangeStreamConfig;
import com.ruoyi.system.service.impl.MongoChangeStreamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * MongoDB Change Streams自动配置类
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "mongodb.change-streams", name = "enabled", havingValue = "true", matchIfMissing = false)
@EnableConfigurationProperties(ChangeStreamConfig.class)
@Import({MongoChangeStreamService.class})
public class MongoChangeStreamAutoConfiguration {

    public MongoChangeStreamAutoConfiguration() {
        log.info("MongoDB Change Streams自动配置已启用");
    }
}
