package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.impl.MongoChangeStreamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * MongoDB Change Streams监控管理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/system/mongo-change-stream")
public class MongoChangeStreamController extends BaseController {

    @Autowired
    private MongoChangeStreamService mongoChangeStreamService;

    /**
     * 获取Change Streams监控状态
     */
    @PreAuthorize("@ss.hasPermi('system:mongo:query')")
    @GetMapping("/status")
    public AjaxResult getStatus() {
        try {
            Map<String, Object> status = mongoChangeStreamService.getMonitoringStatus();
            return AjaxResult.success(status);
        } catch (Exception e) {
            logger.error("获取Change Streams监控状态失败", e);
            return AjaxResult.error("获取监控状态失败: " + e.getMessage());
        }
    }

    /**
     * 启动Change Streams监控
     */
    @PreAuthorize("@ss.hasPermi('system:mongo:start')")
    @PostMapping("/start")
    public AjaxResult startMonitoring() {
        try {
            mongoChangeStreamService.startMonitoring();
            return AjaxResult.success("Change Streams监控已启动");
        } catch (Exception e) {
            logger.error("启动Change Streams监控失败", e);
            return AjaxResult.error("启动监控失败: " + e.getMessage());
        }
    }

    /**
     * 停止Change Streams监控
     */
    @PreAuthorize("@ss.hasPermi('system:mongo:stop')")
    @PostMapping("/stop")
    public AjaxResult stopMonitoring() {
        try {
            mongoChangeStreamService.stopMonitoring();
            return AjaxResult.success("Change Streams监控已停止");
        } catch (Exception e) {
            logger.error("停止Change Streams监控失败", e);
            return AjaxResult.error("停止监控失败: " + e.getMessage());
        }
    }
}
