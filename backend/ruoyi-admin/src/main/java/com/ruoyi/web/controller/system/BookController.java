package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.Book;
import com.ruoyi.system.service.IBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 书本信息Controller
 */
@Anonymous
@RestController
@RequestMapping("/system/book")
public class BookController extends BaseController {

    @Autowired
    private IBookService bookService;

    /**
     * 查询书本信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Book book, @RequestParam(defaultValue = "1") int pageNum,
                             @RequestParam(defaultValue = "10") int pageSize) {
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
        Page<Book> page = bookService.findByCondition(book, pageable);

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setRows(page.getContent());
        rspData.setTotal(page.getTotalElements());
        return rspData;
    }

    /**
     * 根据书本ID获取详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(bookService.findById(id));
    }

    /**
     * 新增书本信息
     */
    @PostMapping
    public AjaxResult add(@RequestBody Book book) {
        return toAjax(bookService.save(book) != null ? 1 : 0);
    }

    /**
     * 修改书本信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody Book book) {
        return toAjax(bookService.update(book) != null ? 1 : 0);
    }

    /**
     * 删除书本信息
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        List<String> idList = java.util.Arrays.asList(ids);
        bookService.deleteByIds(idList);
        return success();
    }

    /**
     * 根据标题搜索书本
     */
    @GetMapping("/search/title/{title}")
    public AjaxResult searchByTitle(@PathVariable String title) {
        return success(bookService.findByTitle(title));
    }

    /**
     * 根据作者搜索书本
     */
    @GetMapping("/search/author/{author}")
    public AjaxResult searchByAuthor(@PathVariable String author) {
        return success(bookService.findByAuthor(author));
    }

    /**
     * 根据标签搜索书本
     */
    @GetMapping("/search/tag/{tag}")
    public AjaxResult searchByTag(@PathVariable String tag) {
        return success(bookService.findByTag(tag));
    }

    /**
     * 获取书本总数
     */
    @GetMapping("/count")
    public AjaxResult count() {
        return success(bookService.count());
    }
}
