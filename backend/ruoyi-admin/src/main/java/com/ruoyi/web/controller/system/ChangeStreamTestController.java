package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Book;
import com.ruoyi.system.service.IBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

/**
 * Change Streams测试控制器
 * 用于测试MongoDB Change Streams监控功能
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/system/change-stream-test")
public class ChangeStreamTestController extends BaseController {

    @Autowired
    private IBookService bookService;

    /**
     * 测试插入操作
     */
    @PostMapping("/test-insert")
    public AjaxResult testInsert() {
        try {
            Book book = new Book();
            book.setTitle("测试书籍 - " + System.currentTimeMillis());
            book.setAuthor("测试作者");
            book.setPublishYear(2024);
            book.setPages(200);
            book.setPrice(new BigDecimal("29.99"));
            book.setIsAvailable(true);
            book.setTags(Arrays.asList("测试", "技术"));
            book.setDescription("这是一本用于测试Change Streams的书籍");
            book.setCreatedAt(new Date());

            Book savedBook = bookService.save(book);
            return AjaxResult.success("插入测试完成", savedBook);
        } catch (Exception e) {
            logger.error("测试插入操作失败", e);
            return AjaxResult.error("插入测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试更新操作
     */
    @PutMapping("/test-update/{id}")
    public AjaxResult testUpdate(@PathVariable String id) {
        try {
            Book book = bookService.findById(id);
            if (book == null) {
                return AjaxResult.error("书籍不存在");
            }

            book.setTitle(book.getTitle() + " - 已更新");
            book.setPrice(book.getPrice().add(new BigDecimal("10.00")));
            book.setIsAvailable(!book.getIsAvailable());

            Book updatedBook = bookService.save(book);
            return AjaxResult.success("更新测试完成", updatedBook);
        } catch (Exception e) {
            logger.error("测试更新操作失败", e);
            return AjaxResult.error("更新测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试删除操作
     */
    @DeleteMapping("/test-delete/{id}")
    public AjaxResult testDelete(@PathVariable String id) {
        try {
            boolean exists = bookService.existsById(id);
            if (!exists) {
                return AjaxResult.error("书籍不存在");
            }

            bookService.deleteById(id);
            return AjaxResult.success("删除测试完成");
        } catch (Exception e) {
            logger.error("测试删除操作失败", e);
            return AjaxResult.error("删除测试失败: " + e.getMessage());
        }
    }

    /**
     * 批量测试操作
     */
    @PostMapping("/test-batch")
    public AjaxResult testBatch() {
        try {
            // 插入测试
            Book book1 = new Book();
            book1.setTitle("批量测试书籍1");
            book1.setAuthor("批量测试作者1");
            book1.setPublishYear(2024);
            book1.setPages(150);
            book1.setPrice(new BigDecimal("19.99"));
            book1.setIsAvailable(true);
            book1.setTags(Arrays.asList("批量测试", "技术"));
            book1.setDescription("批量测试书籍1");
            book1.setCreatedAt(new Date());

            Book savedBook1 = bookService.save(book1);

            // 稍等一下，确保Change Stream能捕获到事件
            Thread.sleep(100);

            // 更新测试
            savedBook1.setTitle(savedBook1.getTitle() + " - 批量更新");
            bookService.save(savedBook1);

            Thread.sleep(100);

            // 删除测试
            bookService.deleteById(savedBook1.getId());

            return AjaxResult.success("批量测试完成");
        } catch (Exception e) {
            logger.error("批量测试失败", e);
            return AjaxResult.error("批量测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有书籍（用于查看测试结果）
     */
    @GetMapping("/books")
    public AjaxResult getAllBooks() {
        try {
            return AjaxResult.success(bookService.findAll());
        } catch (Exception e) {
            logger.error("获取书籍列表失败", e);
            return AjaxResult.error("获取书籍列表失败: " + e.getMessage());
        }
    }
}
