package com.ruoyi.system.service;

import com.ruoyi.system.domain.ChangeStreamEvent;

/**
 * MongoDB Change Stream事件处理器接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IChangeStreamHandler {

    /**
     * 处理变更事件
     * 
     * @param event 变更事件
     */
    void handleChangeEvent(ChangeStreamEvent event);

    /**
     * 获取处理器支持的集合名称
     * 
     * @return 集合名称
     */
    String getSupportedCollection();

    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    String getHandlerName();

    /**
     * 是否启用该处理器
     * 
     * @return true-启用，false-禁用
     */
    default boolean isEnabled() {
        return true;
    }
}
