package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.Book;
import com.ruoyi.system.service.IBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 书本信息管理 服务层实现
 */
@Service
public class BookServiceImpl implements IBookService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Book findById(String id) {
        return mongoTemplate.findById(id, Book.class);
    }

    @Override
    public List<Book> findByTitle(String title) {
        Query query = new Query(Criteria.where("title").regex(title, "i"));
        return mongoTemplate.find(query, Book.class);
    }

    @Override
    public List<Book> findByAuthor(String author) {
        Query query = new Query(Criteria.where("author").regex(author, "i"));
        return mongoTemplate.find(query, Book.class);
    }

    @Override
    public List<Book> findByIsAvailable(Boolean isAvailable) {
        Query query = new Query(Criteria.where("is_available").is(isAvailable));
        return mongoTemplate.find(query, Book.class);
    }

    @Override
    public List<Book> findByTag(String tag) {
        Query query = new Query(Criteria.where("tags").in(tag));
        return mongoTemplate.find(query, Book.class);
    }

    @Override
    public List<Book> findAll() {
        return mongoTemplate.findAll(Book.class);
    }

    @Override
    public Page<Book> findAll(Pageable pageable) {
        Query query = new Query().with(pageable);
        List<Book> books = mongoTemplate.find(query, Book.class);
        return PageableExecutionUtils.getPage(
                books,
                pageable,
                () -> mongoTemplate.count(new Query(), Book.class)
        );
    }

    @Override
    public Page<Book> findByCondition(Book book, Pageable pageable) {
        Query query = buildConditionQuery(book).with(pageable);
        List<Book> books = mongoTemplate.find(query, Book.class);
        return PageableExecutionUtils.getPage(
                books,
                pageable,
                () -> mongoTemplate.count(buildConditionQuery(book), Book.class)
        );
    }

    @Override
    public Book save(Book book) {
        if (book.getCreatedAt() == null) {
            book.setCreatedAt(new Date());
        }
        return mongoTemplate.save(book);
    }

    @Override
    public List<Book> saveAll(List<Book> books) {
        for (Book book : books) {
            if (book.getCreatedAt() == null) {
                book.setCreatedAt(new Date());
            }
        }
        return (List<Book>) mongoTemplate.insertAll(books);
    }

    @Override
    public Book update(Book book) {
        if (book.getId() == null) {
            throw new IllegalArgumentException("书本ID不能为空");
        }

        Query query = new Query(Criteria.where("id").is(book.getId()));
        Update update = new Update();

        if (StringUtils.hasText(book.getTitle())) {
            update.set("title", book.getTitle());
        }
        if (StringUtils.hasText(book.getAuthor())) {
            update.set("author", book.getAuthor());
        }
        if (book.getPublishYear() != null) {
            update.set("publish_year", book.getPublishYear());
        }
        if (book.getPages() != null) {
            update.set("pages", book.getPages());
        }
        if (book.getPrice() != null) {
            update.set("price", book.getPrice());
        }
        if (book.getIsAvailable() != null) {
            update.set("is_available", book.getIsAvailable());
        }
        if (book.getTags() != null && !book.getTags().isEmpty()) {
            update.set("tags", book.getTags());
        }
        if (StringUtils.hasText(book.getDescription())) {
            update.set("description", book.getDescription());
        }

        mongoTemplate.updateFirst(query, update, Book.class);
        return findById(book.getId());
    }

    @Override
    public void deleteById(String id) {
        mongoTemplate.remove(new Query(Criteria.where("id").is(id)), Book.class);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        Query query = new Query(Criteria.where("id").in(ids));
        mongoTemplate.remove(query, Book.class);
    }

    @Override
    public void deleteAll() {
        mongoTemplate.remove(new Query(), Book.class);
    }

    @Override
    public boolean existsById(String id) {
        return mongoTemplate.exists(new Query(Criteria.where("id").is(id)), Book.class);
    }

    @Override
    public long count() {
        return mongoTemplate.count(new Query(), Book.class);
    }

    @Override
    public long countByCondition(Book book) {
        return mongoTemplate.count(buildConditionQuery(book), Book.class);
    }

    /**
     * 构建条件查询
     * @param book 查询条件
     * @return Query对象
     */
    private Query buildConditionQuery(Book book) {
        Query query = new Query();

        if (book == null) {
            return query;
        }

        if (StringUtils.hasText(book.getTitle())) {
            query.addCriteria(Criteria.where("title").regex(book.getTitle(), "i"));
        }
        if (StringUtils.hasText(book.getAuthor())) {
            query.addCriteria(Criteria.where("author").regex(book.getAuthor(), "i"));
        }
        if (book.getPublishYear() != null) {
            query.addCriteria(Criteria.where("publish_year").is(book.getPublishYear()));
        }
        if (book.getIsAvailable() != null) {
            query.addCriteria(Criteria.where("is_available").is(book.getIsAvailable()));
        }
        if (book.getTags() != null && !book.getTags().isEmpty()) {
            query.addCriteria(Criteria.where("tags").in(book.getTags()));
        }
        if (StringUtils.hasText(book.getDescription())) {
            query.addCriteria(Criteria.where("description").regex(book.getDescription(), "i"));
        }

        return query;
    }
}
