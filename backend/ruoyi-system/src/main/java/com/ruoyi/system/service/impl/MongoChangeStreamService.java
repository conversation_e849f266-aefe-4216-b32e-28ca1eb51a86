package com.ruoyi.system.service.impl;

import com.mongodb.client.ChangeStreamIterable;
import com.mongodb.client.MongoChangeStreamCursor;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.changestream.ChangeStreamDocument;
import com.mongodb.client.model.changestream.FullDocument;
import com.ruoyi.system.config.ChangeStreamConfig;
import com.ruoyi.system.domain.ChangeStreamEvent;
import com.ruoyi.system.service.IChangeStreamHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * MongoDB Change Streams监听服务
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@Service
public class MongoChangeStreamService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ChangeStreamConfig changeStreamConfig;

    @Autowired
    private List<IChangeStreamHandler> changeStreamHandlers;

    private final Map<String, IChangeStreamHandler> handlerMap = new ConcurrentHashMap<>();
    private final Map<String, Future<?>> monitorTasks = new ConcurrentHashMap<>();
    private ExecutorService executorService;
    private volatile boolean running = false;

    @PostConstruct
    public void init() {
        if (!changeStreamConfig.isEnabled()) {
            log.info("MongoDB Change Streams监控已禁用");
            return;
        }

        // 初始化处理器映射
        initHandlerMap();

        // 初始化线程池
        executorService = Executors.newCachedThreadPool(r -> {
            Thread thread = new Thread(r, "mongo-change-stream-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });

        // 启动监控
        startMonitoring();
    }

    /**
     * 初始化处理器映射
     */
    private void initHandlerMap() {
        for (IChangeStreamHandler handler : changeStreamHandlers) {
            if (handler.isEnabled()) {
                handlerMap.put(handler.getSupportedCollection(), handler);
                log.info("注册Change Stream处理器: {} -> {}", 
                        handler.getSupportedCollection(), handler.getHandlerName());
            }
        }
    }

    /**
     * 启动监控
     */
    public void startMonitoring() {
        if (running) {
            log.warn("Change Streams监控已在运行中");
            return;
        }

        running = true;
        log.info("启动MongoDB Change Streams监控");

        // 为每个配置的集合启动监控任务
        if (changeStreamConfig.getCollections() != null) {
            for (ChangeStreamConfig.CollectionConfig collectionConfig : changeStreamConfig.getCollections()) {
                if (collectionConfig.isEnabled()) {
                    startCollectionMonitoring(collectionConfig);
                }
            }
        }
    }

    /**
     * 启动单个集合的监控
     */
    private void startCollectionMonitoring(ChangeStreamConfig.CollectionConfig collectionConfig) {
        String collectionName = collectionConfig.getName();
        
        if (!handlerMap.containsKey(collectionName)) {
            log.warn("集合 {} 没有对应的处理器，跳过监控", collectionName);
            return;
        }

        Future<?> task = executorService.submit(() -> {
            monitorCollection(collectionConfig);
        });

        monitorTasks.put(collectionName, task);
        log.info("启动集合 {} 的Change Stream监控", collectionName);
    }

    /**
     * 监控单个集合
     */
    private void monitorCollection(ChangeStreamConfig.CollectionConfig collectionConfig) {
        String collectionName = collectionConfig.getName();
        IChangeStreamHandler handler = handlerMap.get(collectionName);
        
        int reconnectAttempts = 0;
        
        while (running && reconnectAttempts < changeStreamConfig.getMaxReconnectAttempts()) {
            MongoChangeStreamCursor<ChangeStreamDocument<Document>> cursor = null;
            
            try {
                log.info("开始监控集合: {}", collectionName);
                
                // 构建Change Stream
                ChangeStreamIterable<Document> changeStream = mongoTemplate.getCollection(collectionName)
                        .watch(buildPipeline(collectionConfig));
                
                // 配置Change Stream选项
                if (changeStreamConfig.isFullDocument()) {
                    changeStream.fullDocument(FullDocument.UPDATE_LOOKUP);
                }
                
                if (changeStreamConfig.getBatchSize() > 0) {
                    changeStream.batchSize(changeStreamConfig.getBatchSize());
                }
                
                cursor = changeStream.iterator();
                reconnectAttempts = 0; // 重置重连计数
                
                // 监听变更事件
                while (running && cursor.hasNext()) {
                    try {
                        ChangeStreamDocument<Document> changeDoc = cursor.next();
                        processChangeEvent(changeDoc, handler);
                    } catch (Exception e) {
                        log.error("处理Change Stream事件时发生错误", e);
                    }
                }
                
            } catch (Exception e) {
                log.error("Change Stream监控异常，集合: {}, 尝试重连...", collectionName, e);
                reconnectAttempts++;
                
                try {
                    Thread.sleep(changeStreamConfig.getReconnectInterval());
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            } finally {
                if (cursor != null) {
                    try {
                        cursor.close();
                    } catch (Exception e) {
                        log.error("关闭Change Stream游标时发生错误", e);
                    }
                }
            }
        }
        
        if (reconnectAttempts >= changeStreamConfig.getMaxReconnectAttempts()) {
            log.error("集合 {} 的Change Stream监控达到最大重连次数，停止监控", collectionName);
        }
    }

    /**
     * 构建Change Stream管道
     */
    private List<Bson> buildPipeline(ChangeStreamConfig.CollectionConfig collectionConfig) {
        List<Bson> pipeline = new ArrayList<>();
        
        // 添加操作类型过滤
        if (collectionConfig.getOperationTypes() != null && !collectionConfig.getOperationTypes().isEmpty()) {
            pipeline.add(Aggregates.match(
                    Filters.in("operationType", collectionConfig.getOperationTypes())
            ));
        }
        
        // 添加自定义过滤条件
        if (collectionConfig.getFilter() != null && !collectionConfig.getFilter().isEmpty()) {
            Document filterDoc = new Document(collectionConfig.getFilter());
            pipeline.add(Aggregates.match(filterDoc));
        }
        
        return pipeline;
    }

    /**
     * 处理变更事件
     */
    private void processChangeEvent(ChangeStreamDocument<Document> changeDoc, IChangeStreamHandler handler) {
        try {
            // 转换为事件对象
            ChangeStreamEvent event = convertToChangeStreamEvent(changeDoc);
            
            // 调用处理器
            handler.handleChangeEvent(event);
            
        } catch (Exception e) {
            log.error("处理变更事件时发生错误", e);
        }
    }

    /**
     * 转换Change Stream文档为事件对象
     */
    private ChangeStreamEvent convertToChangeStreamEvent(ChangeStreamDocument<Document> changeDoc) {
        ChangeStreamEvent event = new ChangeStreamEvent();
        
        // 设置基本信息
        event.setOperationType(ChangeStreamEvent.OperationType.fromValue(
                changeDoc.getOperationType().getValue()));
        
        // 设置命名空间
        if (changeDoc.getNamespace() != null) {
            ChangeStreamEvent.NameSpace ns = new ChangeStreamEvent.NameSpace();
            ns.setDb(changeDoc.getNamespace().getDatabaseName());
            ns.setColl(changeDoc.getNamespace().getCollectionName());
            event.setNs(ns);
        }
        
        // 设置文档键
        event.setDocumentKey(changeDoc.getDocumentKey());
        
        // 设置完整文档
        event.setFullDocument(changeDoc.getFullDocument());
        
        // 设置更新描述
        if (changeDoc.getUpdateDescription() != null) {
            ChangeStreamEvent.UpdateDescription updateDesc = new ChangeStreamEvent.UpdateDescription();
            updateDesc.setUpdatedFields(changeDoc.getUpdateDescription().getUpdatedFields());
            updateDesc.setRemovedFields(changeDoc.getUpdateDescription().getRemovedFields());
            event.setUpdateDescription(updateDesc);
        }
        
        // 设置集群时间
        event.setClusterTime(changeDoc.getClusterTime());
        
        // 设置处理时间
        event.setProcessedAt(new Date());
        
        return event;
    }

    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (!running) {
            return;
        }
        
        log.info("停止MongoDB Change Streams监控");
        running = false;
        
        // 取消所有监控任务
        for (Map.Entry<String, Future<?>> entry : monitorTasks.entrySet()) {
            try {
                entry.getValue().cancel(true);
                log.info("停止集合 {} 的监控任务", entry.getKey());
            } catch (Exception e) {
                log.error("停止监控任务时发生错误", e);
            }
        }
        
        monitorTasks.clear();
    }

    @PreDestroy
    public void destroy() {
        stopMonitoring();
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    /**
     * 获取监控状态
     */
    public Map<String, Object> getMonitoringStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("running", running);
        status.put("activeCollections", monitorTasks.keySet());
        status.put("handlerCount", handlerMap.size());
        status.put("config", changeStreamConfig);
        return status;
    }
}
