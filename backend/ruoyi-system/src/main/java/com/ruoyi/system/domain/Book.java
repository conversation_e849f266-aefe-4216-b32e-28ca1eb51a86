package com.ruoyi.system.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 书本信息实体类
 * 对应MongoDB集合：mytestbook
 */
@Data
@Document(collection = "mytestbook")
public class Book {

    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 书名
     */
    @Field("title")
    private String title;

    /**
     * 作者
     */
    @Field("author")
    private String author;

    /**
     * 出版年份
     */
    @Field("publish_year")
    private Integer publishYear;

    /**
     * 页数
     */
    @Field("pages")
    private Integer pages;

    /**
     * 价格
     */
    @Field("price")
    private BigDecimal price;

    /**
     * 是否可用
     */
    @Field("is_available")
    private Boolean isAvailable;

    /**
     * 标签
     */
    @Field("tags")
    private List<String> tags;

    /**
     * 描述
     */
    @Field("description")
    private String description;

    /**
     * 创建时间
     */
    @Field("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 创建时间
     */
    @Field("published_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishedDate;


    @Field("isbn")
    private String isbn;
}
