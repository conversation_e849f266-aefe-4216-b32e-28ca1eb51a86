package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.ChangeStreamEvent;
import com.ruoyi.system.service.IChangeStreamHandler;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.stereotype.Service;

/**
 * Book集合(mytestbook)的Change Stream事件处理器
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@Service
public class BookChangeStreamHandler implements IChangeStreamHandler {

    @Override
    public void handleChangeEvent(ChangeStreamEvent event) {
        log.info("处理Book集合变更事件 - 操作类型: {}, 集合: {}", 
                event.getOperationType(), event.getNs().getColl());

        try {
            switch (event.getOperationType()) {
                case INSERT:
                    handleInsert(event);
                    break;
                case UPDATE:
                    handleUpdate(event);
                    break;
                case REPLACE:
                    handleReplace(event);
                    break;
                case DELETE:
                    handleDelete(event);
                    break;
                default:
                    log.info("未处理的操作类型: {}", event.getOperationType());
                    break;
            }
        } catch (Exception e) {
            log.error("处理Book集合变更事件时发生错误", e);
        }
    }

    /**
     * 处理插入事件
     */
    private void handleInsert(ChangeStreamEvent event) {
        Document fullDocument = event.getFullDocument();
        if (fullDocument != null) {
            String bookId = fullDocument.getString("_id");
            String title = fullDocument.getString("title");
            String author = fullDocument.getString("author");
            
            log.info("新书籍插入 - ID: {}, 标题: {}, 作者: {}", bookId, title, author);
            
            // 这里可以添加具体的业务逻辑，例如：
            // 1. 发送通知
            // 2. 更新缓存
            // 3. 同步到其他系统
            // 4. 记录审计日志
            
            // 示例：记录插入日志
            logBookOperation("INSERT", bookId, title, author, null);
        }
    }

    /**
     * 处理更新事件
     */
    private void handleUpdate(ChangeStreamEvent event) {
        Document documentKey = event.getDocumentKey();
        ChangeStreamEvent.UpdateDescription updateDescription = event.getUpdateDescription();
        
        if (documentKey != null && updateDescription != null) {
            String bookId = documentKey.getString("_id");
            Document updatedFields = updateDescription.getUpdatedFields();
            
            log.info("书籍更新 - ID: {}, 更新字段: {}", bookId, updatedFields);
            
            // 处理具体的更新字段
            if (updatedFields.containsKey("title")) {
                String newTitle = updatedFields.getString("title");
                log.info("书籍标题更新 - ID: {}, 新标题: {}", bookId, newTitle);
            }
            
            if (updatedFields.containsKey("is_available")) {
                Boolean isAvailable = updatedFields.getBoolean("is_available");
                log.info("书籍可用状态更新 - ID: {}, 可用状态: {}", bookId, isAvailable);
            }
            
            // 记录更新日志
            logBookOperation("UPDATE", bookId, null, null, updatedFields.toJson());
        }
    }

    /**
     * 处理替换事件
     */
    private void handleReplace(ChangeStreamEvent event) {
        Document fullDocument = event.getFullDocument();
        Document documentKey = event.getDocumentKey();
        
        if (fullDocument != null && documentKey != null) {
            String bookId = documentKey.getString("_id");
            String title = fullDocument.getString("title");
            String author = fullDocument.getString("author");
            
            log.info("书籍替换 - ID: {}, 新标题: {}, 新作者: {}", bookId, title, author);
            
            // 记录替换日志
            logBookOperation("REPLACE", bookId, title, author, null);
        }
    }

    /**
     * 处理删除事件
     */
    private void handleDelete(ChangeStreamEvent event) {
        Document documentKey = event.getDocumentKey();
        
        if (documentKey != null) {
            String bookId = documentKey.getString("_id");
            
            log.info("书籍删除 - ID: {}", bookId);
            
            // 这里可以添加删除后的清理逻辑，例如：
            // 1. 清理相关缓存
            // 2. 通知相关系统
            // 3. 记录删除日志
            
            // 记录删除日志
            logBookOperation("DELETE", bookId, null, null, null);
        }
    }

    /**
     * 记录书籍操作日志
     */
    private void logBookOperation(String operation, String bookId, String title, String author, String details) {
        log.info("书籍操作记录 - 操作: {}, ID: {}, 标题: {}, 作者: {}, 详情: {}", 
                operation, bookId, title, author, details);
        
        // 这里可以将日志保存到数据库或发送到日志系统
        // 例如：保存到操作日志表、发送到Kafka、写入文件等
    }

    @Override
    public String getSupportedCollection() {
        return "mytestbook";
    }

    @Override
    public String getHandlerName() {
        return "BookChangeStreamHandler";
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
