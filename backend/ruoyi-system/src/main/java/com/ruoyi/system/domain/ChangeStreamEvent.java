package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.bson.Document;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * MongoDB Change Stream事件数据模型
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
public class ChangeStreamEvent {

    /**
     * 事件ID
     */
    @Id
    private String id;

    /**
     * 操作类型
     */
    @Field("operationType")
    private OperationType operationType;

    /**
     * 集合名称
     */
    @Field("ns")
    private NameSpace ns;

    /**
     * 文档键
     */
    @Field("documentKey")
    private Document documentKey;

    /**
     * 完整文档（insert和replace操作时包含）
     */
    @Field("fullDocument")
    private Document fullDocument;

    /**
     * 更新描述（update操作时包含）
     */
    @Field("updateDescription")
    private UpdateDescription updateDescription;

    /**
     * 集群时间
     */
    @Field("clusterTime")
    private Object clusterTime;

    /**
     * 事件处理时间
     */
    @Field("processedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processedAt;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        INSERT("insert"),
        UPDATE("update"),
        REPLACE("replace"),
        DELETE("delete"),
        INVALIDATE("invalidate"),
        DROP("drop"),
        DROP_DATABASE("dropDatabase"),
        RENAME("rename");

        private final String value;

        OperationType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static OperationType fromValue(String value) {
            for (OperationType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown operation type: " + value);
        }
    }

    /**
     * 命名空间信息
     */
    @Data
    public static class NameSpace {
        /**
         * 数据库名
         */
        @Field("db")
        private String db;

        /**
         * 集合名
         */
        @Field("coll")
        private String coll;
    }

    /**
     * 更新描述信息
     */
    @Data
    public static class UpdateDescription {
        /**
         * 更新的字段
         */
        @Field("updatedFields")
        private Document updatedFields;

        /**
         * 移除的字段
         */
        @Field("removedFields")
        private java.util.List<String> removedFields;
    }
}
