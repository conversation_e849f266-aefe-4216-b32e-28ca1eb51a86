package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.ChangeStreamEvent;
import com.ruoyi.system.service.IChangeStreamHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 默认的Change Stream事件处理器
 * 可以作为其他集合处理器的模板
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@Service
public class DefaultChangeStreamHandler implements IChangeStreamHandler {

    @Override
    public void handleChangeEvent(ChangeStreamEvent event) {
        log.info("默认处理器处理变更事件 - 操作类型: {}, 集合: {}, 文档ID: {}", 
                event.getOperationType(), 
                event.getNs() != null ? event.getNs().getColl() : "unknown",
                event.getDocumentKey() != null ? event.getDocumentKey().getString("_id") : "unknown");

        // 这里可以添加通用的处理逻辑
        // 例如：记录到审计日志、发送通知等
        
        switch (event.getOperationType()) {
            case INSERT:
                log.debug("文档插入事件 - 集合: {}", event.getNs().getColl());
                break;
            case UPDATE:
                log.debug("文档更新事件 - 集合: {}", event.getNs().getColl());
                break;
            case REPLACE:
                log.debug("文档替换事件 - 集合: {}", event.getNs().getColl());
                break;
            case DELETE:
                log.debug("文档删除事件 - 集合: {}", event.getNs().getColl());
                break;
            default:
                log.debug("其他操作事件 - 类型: {}, 集合: {}", 
                        event.getOperationType(), event.getNs().getColl());
                break;
        }
    }

    @Override
    public String getSupportedCollection() {
        // 返回空字符串表示这是一个默认处理器，不绑定特定集合
        return "";
    }

    @Override
    public String getHandlerName() {
        return "DefaultChangeStreamHandler";
    }

    @Override
    public boolean isEnabled() {
        // 默认处理器通常不启用，除非没有其他处理器
        return false;
    }
}
