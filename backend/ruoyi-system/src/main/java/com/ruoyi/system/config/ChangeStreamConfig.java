package com.ruoyi.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * MongoDB Change Streams配置类
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@Component
@ConfigurationProperties(prefix = "mongodb.change-streams")
public class ChangeStreamConfig {

    /**
     * 是否启用Change Streams监控
     */
    private boolean enabled = true;

    /**
     * 数据库名称
     */
    private String database = "wendao_knowledge_store";

    /**
     * 监控的集合配置列表
     */
    private List<CollectionConfig> collections;

    /**
     * 重连间隔时间（毫秒）
     */
    private long reconnectInterval = 5000;

    /**
     * 最大重连次数
     */
    private int maxReconnectAttempts = 10;

    /**
     * 是否启用全文档返回
     */
    private boolean fullDocument = true;

    /**
     * 批处理大小
     */
    private int batchSize = 100;

    /**
     * 集合配置
     */
    @Data
    public static class CollectionConfig {
        /**
         * 集合名称
         */
        private String name;

        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 监控的操作类型
         */
        private List<String> operationTypes;

        /**
         * 过滤条件
         */
        private Map<String, Object> filter;

        /**
         * 处理器名称
         */
        private String handlerName;
    }
}
