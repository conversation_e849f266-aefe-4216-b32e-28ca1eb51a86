package com.ruoyi.system.service;

import com.ruoyi.system.domain.Book;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 书本信息管理 服务层
 */
public interface IBookService {

    /**
     * 根据ID查询书本信息
     * @param id 书本ID
     * @return 书本信息
     */
    Book findById(String id);

    /**
     * 根据书名查询书本信息
     * @param title 书名
     * @return 书本信息列表
     */
    List<Book> findByTitle(String title);

    /**
     * 根据作者查询书本信息
     * @param author 作者
     * @return 书本信息列表
     */
    List<Book> findByAuthor(String author);

    /**
     * 根据可用状态查询书本信息
     * @param isAvailable 是否可用
     * @return 书本信息列表
     */
    List<Book> findByIsAvailable(Boolean isAvailable);

    /**
     * 根据标签查询书本信息
     * @param tag 标签
     * @return 书本信息列表
     */
    List<Book> findByTag(String tag);

    /**
     * 查询所有书本信息
     * @return 书本信息列表
     */
    List<Book> findAll();

    /**
     * 分页查询书本信息
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Book> findAll(Pageable pageable);

    /**
     * 根据条件分页查询书本信息
     * @param book 查询条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Book> findByCondition(Book book, Pageable pageable);

    /**
     * 新增书本信息
     * @param book 书本信息
     * @return 保存的书本信息
     */
    Book save(Book book);

    /**
     * 批量新增书本信息
     * @param books 书本信息列表
     * @return 保存的书本信息列表
     */
    List<Book> saveAll(List<Book> books);

    /**
     * 更新书本信息
     * @param book 书本信息
     * @return 更新的书本信息
     */
    Book update(Book book);

    /**
     * 根据ID删除书本信息
     * @param id 书本ID
     */
    void deleteById(String id);

    /**
     * 批量删除书本信息
     * @param ids 书本ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 删除所有书本信息
     */
    void deleteAll();

    /**
     * 根据ID判断书本是否存在
     * @param id 书本ID
     * @return 是否存在
     */
    boolean existsById(String id);

    /**
     * 统计书本总数
     * @return 总数
     */
    long count();

    /**
     * 根据条件统计书本数量
     * @param book 查询条件
     * @return 数量
     */
    long countByCondition(Book book);
}
