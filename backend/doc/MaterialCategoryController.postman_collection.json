{"info": {"_postman_id": "material-category-controller-api", "name": "MaterialCategoryController API", "description": "素材分组管理相关API接口", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "获取素材分组列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/list?pageNum=1&pageSize=10&name={{name}}&type={{type}}&parentId={{parentId}}", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "list"], "query": [{"key": "pageNum", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "10", "description": "每页数量"}, {"key": "name", "value": "{{name}}", "description": "分类名称(模糊查询)"}, {"key": "type", "value": "{{type}}", "description": "分组类型:1-图片 2-音频 3-视频 4-电子书 5-文档"}, {"key": "parentId", "value": "{{parentId}}", "description": "父分类ID"}]}, "description": "分页查询素材分组列表，支持按名称、类型、父分类ID筛选"}}, {"name": "根据ID获取素材分组详情", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/{{id}}", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "{{id}}"]}, "description": "根据分组ID获取素材分组详细信息"}}, {"name": "新增素材分组", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"parentId\": 0,\n  \"name\": \"图片分组\",\n  \"type\": 1,\n  \"categoryCount\": 0,\n  \"sortOrder\": 1,\n  \"isAllowDelete\": 1\n}"}, "url": {"raw": "{{baseUrl}}/system/materialCategory", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory"]}, "description": "新增素材分组信息"}}, {"name": "修改素材分组", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"parentId\": 0,\n  \"name\": \"修改后的分组名称\",\n  \"type\": 1,\n  \"categoryCount\": 5,\n  \"sortOrder\": 2,\n  \"isAllowDelete\": 1\n}"}, "url": {"raw": "{{baseUrl}}/system/materialCategory", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory"]}, "description": "修改素材分组信息"}}, {"name": "删除素材分组", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/1,2,3", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "1,2,3"]}, "description": "批量删除素材分组，多个ID用逗号分隔。注意：不允许删除的分组、有子分组的分组、有素材的分组都不能删除"}}, {"name": "根据父分类ID获取子分类列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/children/{{parentId}}", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "children", "{{parentId}}"]}, "description": "根据父分类ID获取所有子分类列表"}}, {"name": "根据类型获取分组列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/type/{{type}}", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "type", "{{type}}"]}, "description": "根据分组类型获取分组列表，type: 1-图片 2-音频 3-视频 4-电子书 5-文档"}}, {"name": "更新分组素材数量", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/count/update/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "count", "update", "{{categoryId}}"]}, "description": "重新统计并更新分组下的素材数量"}}, {"name": "增加分组素材数量", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/count/increase/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "count", "increase", "{{categoryId}}"]}, "description": "增加分组的素材计数"}}, {"name": "减少分组素材数量", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/materialCategory/count/decrease/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["system", "materialCategory", "count", "decrease", "{{categoryId}}"]}, "description": "减少分组的素材计数"}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "your-jwt-token-here", "type": "string"}, {"key": "id", "value": "1", "type": "string"}, {"key": "parentId", "value": "0", "type": "string"}, {"key": "categoryId", "value": "1", "type": "string"}, {"key": "type", "value": "1", "type": "string"}, {"key": "name", "value": "图片分组", "type": "string"}]}