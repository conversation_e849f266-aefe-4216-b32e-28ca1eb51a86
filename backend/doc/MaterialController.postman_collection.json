{"info": {"_postman_id": "material-controller-api", "name": "MaterialController API", "description": "素材管理相关API接口", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "获取素材列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/list?pageNum=1&pageSize=10&title={{title}}&type={{type}}&categoryId={{categoryId}}", "host": ["{{baseUrl}}"], "path": ["system", "material", "list"], "query": [{"key": "pageNum", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "10", "description": "每页数量"}, {"key": "title", "value": "{{title}}", "description": "素材名称(模糊查询)"}, {"key": "type", "value": "{{type}}", "description": "素材类型:1-图片 2-音频 3-视频 4-电子书 5-文档"}, {"key": "categoryId", "value": "{{categoryId}}", "description": "分组ID"}]}, "description": "分页查询素材列表，支持按名称、类型、分组ID筛选"}}, {"name": "根据ID获取素材详情", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/{{materialId}}", "host": ["{{baseUrl}}"], "path": ["system", "material", "{{materialId}}"]}, "description": "根据素材ID获取素材详细信息"}}, {"name": "新增素材", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"materialId\": \"material_001\",\n  \"title\": \"示例素材\",\n  \"type\": 1,\n  \"categoryId\": 1,\n  \"materialSize\": 1024.5,\n  \"referCount\": 0,\n  \"viewCount\": 0,\n  \"width\": 800,\n  \"height\": 600,\n  \"length\": null,\n  \"patchImgUrl\": \"https://example.com/image.jpg\",\n  \"pixelData\": \"800*600\"\n}"}, "url": {"raw": "{{baseUrl}}/system/material", "host": ["{{baseUrl}}"], "path": ["system", "material"]}, "description": "新增素材信息"}}, {"name": "修改素材", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"materialId\": \"material_001\",\n  \"title\": \"修改后的素材名称\",\n  \"type\": 1,\n  \"categoryId\": 2,\n  \"materialSize\": 2048.0,\n  \"width\": 1024,\n  \"height\": 768,\n  \"patchImgUrl\": \"https://example.com/new_image.jpg\",\n  \"pixelData\": \"1024*768\"\n}"}, "url": {"raw": "{{baseUrl}}/system/material", "host": ["{{baseUrl}}"], "path": ["system", "material"]}, "description": "修改素材信息"}}, {"name": "删除素材", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/material_001,material_002", "host": ["{{baseUrl}}"], "path": ["system", "material", "material_001,material_002"]}, "description": "批量删除素材，多个ID用逗号分隔"}}, {"name": "根据分类ID获取素材列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/category/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["system", "material", "category", "{{categoryId}}"]}, "description": "根据分类ID获取该分类下的所有素材"}}, {"name": "根据类型获取素材列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/type/{{type}}", "host": ["{{baseUrl}}"], "path": ["system", "material", "type", "{{type}}"]}, "description": "根据素材类型获取素材列表，type: 1-图片 2-音频 3-视频 4-电子书 5-文档"}}, {"name": "增加素材引用数", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/refer/increase/{{materialId}}", "host": ["{{baseUrl}}"], "path": ["system", "material", "refer", "increase", "{{materialId}}"]}, "description": "增加素材的引用计数"}}, {"name": "减少素材引用数", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/refer/decrease/{{materialId}}", "host": ["{{baseUrl}}"], "path": ["system", "material", "refer", "decrease", "{{materialId}}"]}, "description": "减少素材的引用计数"}}, {"name": "增加素材访问数", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "shopToken", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/system/material/view/increase/{{materialId}}", "host": ["{{baseUrl}}"], "path": ["system", "material", "view", "increase", "{{materialId}}"]}, "description": "增加素材的访问计数"}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "your-jwt-token-here", "type": "string"}, {"key": "materialId", "value": "material_001", "type": "string"}, {"key": "categoryId", "value": "1", "type": "string"}, {"key": "type", "value": "1", "type": "string"}, {"key": "title", "value": "测试素材", "type": "string"}]}