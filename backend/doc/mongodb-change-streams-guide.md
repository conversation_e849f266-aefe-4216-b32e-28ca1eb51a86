# MongoDB Change Streams监控功能使用指南

## 功能概述

本系统实现了MongoDB Change Streams Pipeline监控数据变更的功能，可以实时监控指定集合的数据变更事件（插入、更新、替换、删除等），并通过可配置的处理器进行相应的业务处理。

## 核心组件

### 1. 配置类
- `ChangeStreamConfig`: Change Streams配置管理
- `MongoChangeStreamAutoConfiguration`: 自动配置类

### 2. 数据模型
- `ChangeStreamEvent`: 变更事件数据模型
- `ChangeStreamEvent.OperationType`: 操作类型枚举
- `ChangeStreamEvent.NameSpace`: 命名空间信息
- `ChangeStreamEvent.UpdateDescription`: 更新描述信息

### 3. 服务接口和实现
- `IChangeStreamHandler`: 变更事件处理器接口
- `BookChangeStreamHandler`: Book集合的具体处理器实现
- `DefaultChangeStreamHandler`: 默认处理器模板
- `MongoChangeStreamService`: 核心监听服务

### 4. 控制器
- `MongoChangeStreamController`: 监控管理控制器
- `ChangeStreamTestController`: 测试控制器

## 配置说明

在 `application.yml` 中添加以下配置：

```yaml
# MongoDB Change Streams配置
mongodb:
  change-streams:
    # 是否启用Change Streams监控
    enabled: true
    # 数据库名称
    database: wendao_knowledge_store
    # 重连间隔时间（毫秒）
    reconnect-interval: 5000
    # 最大重连次数
    max-reconnect-attempts: 10
    # 是否启用全文档返回
    full-document: true
    # 批处理大小
    batch-size: 100
    # 监控的集合配置
    collections:
      - name: mytestbook
        enabled: true
        operation-types:
          - insert
          - update
          - replace
          - delete
        handler-name: BookChangeStreamHandler
```

## 使用方法

### 1. 启动监控

系统启动时会自动启动Change Streams监控。也可以通过API手动控制：

```bash
# 启动监控
POST /system/mongo-change-stream/start

# 停止监控
POST /system/mongo-change-stream/stop

# 查看监控状态
GET /system/mongo-change-stream/status
```

### 2. 添加新的集合监控

#### 步骤1: 创建处理器

```java
@Slf4j
@Service
public class YourCollectionChangeStreamHandler implements IChangeStreamHandler {

    @Override
    public void handleChangeEvent(ChangeStreamEvent event) {
        // 处理变更事件的业务逻辑
        log.info("处理{}集合的{}操作", 
                event.getNs().getColl(), event.getOperationType());
    }

    @Override
    public String getSupportedCollection() {
        return "your_collection_name";
    }

    @Override
    public String getHandlerName() {
        return "YourCollectionChangeStreamHandler";
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
```

#### 步骤2: 添加配置

在 `application.yml` 的 `collections` 列表中添加新的集合配置：

```yaml
collections:
  - name: mytestbook
    enabled: true
    operation-types:
      - insert
      - update
      - replace
      - delete
    handler-name: BookChangeStreamHandler
  - name: your_collection_name
    enabled: true
    operation-types:
      - insert
      - update
    handler-name: YourCollectionChangeStreamHandler
```

### 3. 测试功能

使用测试控制器验证Change Streams功能：

```bash
# 测试插入
POST /system/change-stream-test/test-insert

# 测试更新
PUT /system/change-stream-test/test-update/{id}

# 测试删除
DELETE /system/change-stream-test/test-delete/{id}

# 批量测试
POST /system/change-stream-test/test-batch

# 查看所有书籍
GET /system/change-stream-test/books
```

## 处理器开发指南

### 基本结构

```java
@Service
public class CustomChangeStreamHandler implements IChangeStreamHandler {

    @Override
    public void handleChangeEvent(ChangeStreamEvent event) {
        switch (event.getOperationType()) {
            case INSERT:
                handleInsert(event);
                break;
            case UPDATE:
                handleUpdate(event);
                break;
            case DELETE:
                handleDelete(event);
                break;
            // ... 其他操作类型
        }
    }

    private void handleInsert(ChangeStreamEvent event) {
        // 处理插入事件
        Document fullDocument = event.getFullDocument();
        // 业务逻辑...
    }

    private void handleUpdate(ChangeStreamEvent event) {
        // 处理更新事件
        Document updatedFields = event.getUpdateDescription().getUpdatedFields();
        // 业务逻辑...
    }

    private void handleDelete(ChangeStreamEvent event) {
        // 处理删除事件
        Document documentKey = event.getDocumentKey();
        String deletedId = documentKey.getString("_id");
        // 业务逻辑...
    }
}
```

### 常见用例

1. **缓存更新**: 当数据变更时自动更新Redis缓存
2. **消息通知**: 发送变更通知到消息队列
3. **数据同步**: 同步数据到其他系统
4. **审计日志**: 记录数据变更历史
5. **搜索索引**: 更新Elasticsearch索引

## 注意事项

1. **MongoDB版本要求**: Change Streams需要MongoDB 3.6+版本，且必须是副本集或分片集群
2. **权限要求**: 需要对目标数据库有相应的读取权限
3. **网络稳定性**: 建议在稳定的网络环境中使用，系统会自动处理重连
4. **性能考虑**: 大量变更时注意处理器的性能，避免阻塞
5. **异常处理**: 处理器中的异常不会影响其他处理器的执行

## 监控和调试

- 查看日志文件了解Change Streams的运行状态
- 使用 `/system/mongo-change-stream/status` API查看监控状态
- 通过测试控制器验证功能是否正常工作

## 扩展功能

系统支持以下扩展：

1. **过滤条件**: 在配置中添加filter字段来过滤特定的变更事件
2. **多处理器**: 一个集合可以配置多个处理器
3. **动态配置**: 支持运行时动态添加或移除监控集合
4. **批处理**: 支持批量处理变更事件以提高性能
